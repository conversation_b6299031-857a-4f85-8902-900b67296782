{% set app_id %}co.ujet.ios.v3.example{% endset %}
{% set screenshare_app_id%}{{ app_id }}.screenshare{% endset %}
{% set code_sign_identity %}Apple Distribution: <PERSON><PERSON> (8694345EB8){% endset %}
name: ExampleApp
options:
  deploymentTarget:
    iOS: {{ deployment_target }}
packages:
  CCAIKit:
    path: ../Distribute
  CobrowseSDK:
    github: cobrowseio/cobrowse-sdk-ios-binary
    from: {{ cobrowse_io }}
settingGroups:
  targetedDeviceFamily:
    TARGETED_DEVICE_FAMILY: 1
    ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
    ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME: AccentColor
settings:
  base:
    ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS: NO
    CODE_SIGN_STYLE: Manual
    CURRENT_PROJECT_VERSION: {{ build_number }}
    DEBUG_INFORMATION_FORMAT: "dwarf-with-dsym"
    DEVELOPMENT_TEAM: 8694345EB8
    GENERATE_INFOPLIST_FILE: true
    MARKETING_VERSION: {{ ujet }}
    PRODUCT_BUNDLE_IDENTIFIER: {{ app_id }}
  configs:
    release:
      PROVISIONING_PROFILE_SPECIFIER: match AppStore {{ app_id }}
targets:
  {% if Empty %}
  Empty:
    templates: [App]
    settings:
      groups:
        - targetedDeviceFamily
      configs:
        release:
          CODE_SIGN_IDENTITY: "{{ code_sign_identity }}"
  {% endif %}
  {% if ChatRed %}
  ChatRed:
    templates: [App]
    settings:
      groups:
        - targetedDeviceFamily
      configs:
        release:
          CODE_SIGN_IDENTITY: "{{ code_sign_identity }}"
    dependencies:
      - package: CCAIKit
  {% endif %}
  {% if ScreenShare %}
  ScreenShare:
    templates: [App]
    settings:
      groups:
        - targetedDeviceFamily
      configs:
        release:
          CODE_SIGN_IDENTITY: "{{ code_sign_identity }}"
    dependencies:
      - package: CCAIKit
      - framework: ../Distribute/CCAIScreenShare.xcframework
      - target: ScreenShareExtension
        embed: true
        codeSign: false
        buildPhase:
          copyFiles:
            destination: plugins
  {% endif %}
  ScreenShareExtension:
    templates: [Extension]
    dependencies:
      - package: CobrowseSDK
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: {{ screenshare_app_id }}
      configs:
        release:
          CODE_SIGN_IDENTITY: "{{ code_sign_identity }}"
          PROVISIONING_PROFILE_SPECIFIER: match AppStore {{ screenshare_app_id }}
    
targetTemplates:
  App:
    type: application
    platform: iOS
    scheme: {}
    sources:
      - path: ${target_name}
      - path: ../App/Assets.xcassets
        type: folder
    info:
      path: Info.plist
      properties:
        CBIOBroadcastExtension: {{ screenshare_app_id }}
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleURLTypes:
          - CFBundleTypeRole: Editor
            CFBundleURLName: {{ app_id }}
            CFBundleURLSchemes:
              - ccai
        CFBundleVersion: {{ build_number }}
        NSCameraUsageDescription: "Camera Usage Description"
        NSFaceIDUsageDescription: "FaceID Usage Description"
        NSMicrophoneUsageDescription: "Microphone Usage Description"
        NSPhotoLibraryUsageDescription: "Photo Library Usage Description"
        NSBluetoothAlwaysUsageDescription: "Bluetooth Usage Description"
        NSLocationWhenInUseUsageDescription: "Location Usage Description"
        UIBackgroundModes:
          - audio
          - remote-notification
          - voip
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
    entitlements:
      path: app.entitlements
      properties:
        aps-environment: development
        keychain-access-groups: 
          - $(AppIdentifierPrefix)io.cobrowse

  Extension:
    type: app-extension
    platform: iOS
    sources:
      - path: ../ScreenShareExtension
        excludes:
          - Info.plist
    info:
      path: Info-${target_name}.plist
      properties:
        CFBundleDisplayName: ScreenShareExtension
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: {{ build_number }}
        NSExtension:
          NSExtensionPointIdentifier: com.apple.broadcast-services-upload
          NSExtensionPrincipalClass: SampleHandler
          RPBroadcastProcessMode: RPBroadcastProcessModeSampleBuffer
    entitlements:
      path: ${target_name}.entitlements
      properties:
        keychain-access-groups: 
          - $(AppIdentifierPrefix)io.cobrowse
