module Fastlane
  module Actions
    class SubmitIosTestflightAction < Action
      def self.run(params)
        if params[:build_number] && !params[:build_number].empty?
          build_number = params[:build_number].to_i
        else
          build_number = latest_build_number(params)
        end
        
        app_types = params[:app_types]
        
        UI.message("Building and deploying ExampleApp (#{app_types.join(', ')}) with build number: #{build_number}")
        
        # Step 1: Generate ExampleApp project
        UI.message("Step 1: Generating ExampleApp project...")
        other_action.generate_ios_example_project(
          build_number: build_number,
          app_types: app_types
        )
        
        # Step 2: Run match with appstore type
        unless params[:skip_match]
          UI.message("Step 2: Setting up code signing with match...")
          setup_code_signing(params)
        else
          UI.message("Step 2: Skipping code signing setup (skip_match=true)")
        end
        
        # Step 3: Build the app with gym
        unless params[:skip_build]
          UI.message("Step 3: Building the app...")
          build_app(params)
        else
          UI.message("Step 3: Skipping app build (skip_build=true)")
        end
        
        # Step 4: Upload to TestFlight with pilot
        unless params[:skip_testflight]
          UI.message("Step 4: Uploading to TestFlight...")
          upload(params)
        else
          UI.message("Step 4: Skipping TestFlight upload (skip_testflight=true)")
        end
        
        UI.success("Successfully built and deployed ExampleApp!")
      end
      
      private

      def self.latest_build_number(params)
        api_key = other_action.app_store_connect_api_key(
          duration: 1200
        )
        other_action.latest_testflight_build_number(
          api_key: api_key,
          app_identifier: params[:app_id],
          version: other_action.get_sdk_version(platform: 'ios')
        )
      end
      
      def self.setup_code_signing(params)
        app_identifier = params[:app_id]
        extension_identifier = "#{params[:app_id]}.screenshare"

        other_action.sync_code_signing(
          app_identifier: [app_identifier, extension_identifier],
          team_name: 'Hisun Kim',
          team_id: '8694345EB8',
          type: 'appstore',
          git_branch: 'example_app',
          force_for_new_devices: false,
          readonly: params[:match_readonly]
        )
      end
      
      def self.build_app(params)
        app_identifier = params[:app_id]
        extension_identifier = "#{params[:app_id]}.screenshare"
        app_types = params[:app_types]

        app_types.each do |app_type|
          other_action.increment_build_number(
            xcodeproj: 'ios/ExampleApp/ExampleApp.xcodeproj'
          )
          other_action.build_ios_app(
            project: 'ios/ExampleApp/ExampleApp.xcodeproj',
            scheme: app_type,
            derived_data_path: './build/ExampleApp/DerivedData',
            output_directory: './build/ExampleApp',
            output_name: "ExampleApp-#{app_type}.ipa",
            export_method: 'app-store',
            export_team_id: '8694345EB8',
            export_options: {
              provisioningProfiles: {
                app_identifier => "match AppStore #{app_identifier}",
                extension_identifier => "match AppStore #{extension_identifier}"
              }
            }
          )
          UI.success("Successfully built #{app_type}!")
        end
      end
      
      def self.upload(params)
        app_types = params[:app_types]

        api_key = other_action.app_store_connect_api_key(
          duration: 1200
        )

        app_types.each do |app_type|
          other_action.upload_to_testflight(
            app_identifier: params[:app_id],
            ipa: "./build/ExampleApp/ExampleApp-#{app_type}.ipa",
            api_key: api_key,
            skip_waiting_for_build_processing: true,
            skip_submission: true
          )
          UI.success("Successfully uploaded #{app_type}!")
        end
      end

      #####################################################
      # @!group Documentation
      #####################################################

      def self.description
        "Build and deploy ExampleApp to TestFlight"
      end

      def self.details
        "This action:
        1. Generates ExampleApp project using genesis templates
        2. Sets up code signing with match (appstore type)
        3. Builds the app using gym
        4. Uploads to TestFlight using pilot"
      end

      def self.available_options
        [
          FastlaneCore::ConfigItem.new(
            key: :app_store_connect_api_key_key_id,
            env_name: "APP_STORE_CONNECT_API_KEY_KEY_ID",
            description: "App Store Connect API Key ID",
            type: String,
            is_string: true,
            verify_block: proc do |value|
              UI.user_error!("App Store Connect API Key ID cannot be empty") if value.to_s.empty?
            end
          ),
          FastlaneCore::ConfigItem.new(
            key: :app_store_connect_api_key_issuer_id,
            env_name: "APP_STORE_CONNECT_API_KEY_ISSUER_ID",
            description: "App Store Connect API Key Issuer ID",
            type: String,
            is_string: true,
            verify_block: proc do |value|
              UI.user_error!("App Store Connect API Key Issuer ID cannot be empty") if value.to_s.empty?
            end
          ),
          FastlaneCore::ConfigItem.new(
            key: :app_store_connect_api_key_key_filepath,
            env_name: "APP_STORE_CONNECT_API_KEY_KEY_FILEPATH",
            description: "Path to the App Store Connect API Key file",
            type: String,
            is_string: true,
            verify_block: proc do |value|
              UI.user_error!("App Store Connect API Key file path cannot be empty") if value.to_s.empty?
            end
          ),
          FastlaneCore::ConfigItem.new(
            key: :app_id,
            env_name: "SUBMIT_IOS_TESTFLIGHT_APP_ID",
            description: "App ID to use for the ExampleApp",
            type: String,
            is_string: true,
            default_value: 'co.ujet.ios.v3.example',
            verify_block: proc do |value|
              UI.user_error!("App ID cannot be empty") if value.to_s.empty?
            end
          ),
          FastlaneCore::ConfigItem.new(
            key: :build_number,
            env_name: "SUBMIT_IOS_TESTFLIGHT_BUILD_NUMBER",
            description: "Build number to use for the ExampleApp",
            optional: true
          ),
          FastlaneCore::ConfigItem.new(
            key: :app_types,
            env_name: "SUBMIT_IOS_TESTFLIGHT_APP_TYPES",
            description: "Type of ExampleApp to build",
            type: Array,
            default_value: ['Empty', 'ChatRed', 'ScreenShare']
          ),
          FastlaneCore::ConfigItem.new(
            key: :match_readonly,
            env_name: "SUBMIT_IOS_TESTFLIGHT_MATCH_READONLY",
            description: "Whether to run match in readonly mode",
            type: FalseClass,
            default_value: true,
            optional: true
          ),
          FastlaneCore::ConfigItem.new(
            key: :skip_match,
            env_name: "SUBMIT_IOS_TESTFLIGHT_SKIP_MATCH",
            description: "Skip match code signing setup for testing purposes",
            type: TrueClass,
            default_value: false,
            optional: true
          ),
          FastlaneCore::ConfigItem.new(
            key: :skip_build,
            env_name: "SUBMIT_IOS_TESTFLIGHT_SKIP_BUILD",
            description: "Skip app build for testing purposes",
            type: TrueClass,
            default_value: false,
            optional: true
          ),
          FastlaneCore::ConfigItem.new(
            key: :skip_testflight,
            env_name: "SUBMIT_IOS_TESTFLIGHT_SKIP_TESTFLIGHT",
            description: "Skip TestFlight upload for testing purposes",
            type: TrueClass,
            default_value: false,
            optional: true
          )
        ]
      end

      def self.output
        []
      end

      def self.return_value
        "Returns nothing"
      end

      def self.authors
        ["UJET Mobile Team"]
      end

      def self.is_supported?(platform)
        platform == :ios
      end

      def self.example_code
        [
          'build_and_deploy_example_app(build_number: "123")',
          'build_and_deploy_example_app(build_number: "456", app_types: ["ChatRed", "ScreenShare"])',
          'build_and_deploy_example_app(build_number: ENV["BUILD_NUMBER"], app_types: ["Empty", "ChatRed"])',
          'build_and_deploy_example_app(build_number: "789", skip_build: true, skip_testflight: true)'
        ]
      end

      def self.category
        :building
      end
    end
  end
end
