module Fastlane
  module Actions
    class GenerateIosExampleProjectAction < Action
      def self.run(params)
        build_number = params[:build_number]
        app_types = params[:app_types]

        UI.message("Generating iOS Example App projects with build number: #{build_number}")

        # Generate Package.swift
        UI.message("Generating Package.swift...")
        generate_package_swift

        Action.sh("rm -rf ios/ExampleApp")

        # Generate Swift app files for each type
        app_types.each do |type|
          UI.message("Generating #{type} app...")
          generate_app_file(type)
        end

        # Generate project.yml
        UI.message("Generating project.yml...")
        generate_project_yml(build_number, app_types)

        # Generate Xcode project
        UI.message("Generating Xcode project...")
        generate_xcode_project

        UI.success("Successfully generated iOS Example App projects!")
      end

      private

      def self.generate_package_swift
        command = [
          'mint', 'run', 'genesis', 'generate',
          'genesis/SwiftPackage/template.yml',
          '-p', 'genesis/versions.yml',
          '-o', 'local:true'
        ].join(' ')

        UI.message("Command: #{command}")
        Actions.sh(command)
      end

      def self.generate_app_file(type)
        command = [
          'mint', 'run', 'genesis', 'generate',
          'genesis/ExampleApp/app_template.yml',
          '--options', "type:#{type}"
        ].join(' ')

        UI.message("Command: #{command}")
        Actions.sh(command)
      end

      def self.generate_project_yml(build_number, app_types)
        command = [
          'mint', 'run', 'genesis', 'generate',
          'genesis/ExampleApp/project_template.yml',
          '-p', 'genesis/versions.yml',
          '-o', "\"build_number:#{build_number}, #{app_types.map { |type| "#{type}:true" }.join(', ')}\""
        ].join(' ')

        UI.message("Command: #{command}")
        Actions.sh(command)
      end

      def self.generate_xcode_project
        command = [
          'mint', 'run', 'xcodegen', 'generate',
          '--spec', 'ios/ExampleApp/project.yml'
        ].join(' ')

        UI.message("Command: #{command}")
        Actions.sh(command)
      end

      #####################################################
      # @!group Documentation
      #####################################################

      def self.description
        "Generate iOS Example App projects using Genesis templates"
      end

      def self.details
        "This action generates iOS Example App projects by:
        1. Generating Swift app files for Empty, Chat, and ChatSwiftUI types using Genesis templates
        2. Generating project.yml with specified build number
        3. Generating Xcode project using XcodeGen"
      end

      def self.available_options
        [
          FastlaneCore::ConfigItem.new(
            key: :build_number,
            env_name: "GENERATE_IOS_EXAMPLE_PROJECT_BUILD_NUMBER",
            description: "Build number to use for the generated projects",
            optional: true,
            type: Integer,
            default_value: 1
          ),
          FastlaneCore::ConfigItem.new(
            key: :app_types,
            env_name: "GENERATE_IOS_EXAMPLE_PROJECT_APP_TYPES",
            description: "Types of ExampleApp to generate",
            type: Array,
            is_string: true,
            default_value: ['Empty', 'ChatRed', 'ScreenShare']
          )
        ]
      end

      def self.output
        []
      end

      def self.return_value
        "Returns nothing"
      end

      def self.authors
        ["UJET Mobile Team"]
      end

      def self.is_supported?(platform)
        platform == :ios
      end

      def self.example_code
        [
          'generate_ios_example_project(build_number: "123")',
          'generate_ios_example_project(build_number: ENV["BUILD_NUMBER"])'
        ]
      end

      def self.category
        :project
      end
    end
  end
end