fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios setup

```sh
[bundle exec] fastlane ios setup
```

development setup

### ios my_test

```sh
[bundle exec] fastlane ios my_test
```



### ios release

```sh
[bundle exec] fastlane ios release
```

Release the app

### ios get_simulator_device_id

```sh
[bundle exec] fastlane ios get_simulator_device_id
```

Get simulator device ID

----


## Android

### android lint

```sh
[bundle exec] fastlane android lint
```



### android release

```sh
[bundle exec] fastlane android release
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
